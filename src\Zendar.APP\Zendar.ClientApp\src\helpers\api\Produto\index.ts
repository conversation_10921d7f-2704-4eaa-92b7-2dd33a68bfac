export { buscarTamanhosProduto as buscarTamanhosDoProduto } from './buscarTamanhosProduto';
export { buscarCoresProduto as buscarCoresDoProduto } from './buscarCoresProduto';
export { buscarCoresTamanhosProduto as buscarVariacoesProduto } from './buscarCoresTamanhosProduto';
export { buscarVariacoesProduto as obterVariacoesProduto } from './buscarVariacoesProduto';

export type {
  ProdutoTamanhoProps,
  TamanhoOption,
} from './buscarTamanhosProduto';

export type {
  CorDetalhes,
  ProdutoCoresProps,
  CorOption,
} from './buscarCoresProduto';

export type {
  VariacaoProduto,
  ParametrosObterVariacoes,
} from './buscarVariacoesProduto';

export type { VariacoesProdutoResult } from './buscarCoresTamanhosProduto';
