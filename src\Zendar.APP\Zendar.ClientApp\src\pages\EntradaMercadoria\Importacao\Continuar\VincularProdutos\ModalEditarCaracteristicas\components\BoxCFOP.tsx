import { Flex, Text } from '@chakra-ui/react';

import Input from 'components/PDV/Input';

import { VinculacaoProps } from '../types';

import { BoxCaracteristica } from './BoxCaracteristica';

interface Props {
  dadosVinculacao: VinculacaoProps;
}

export function BoxCFOP({ dadosVinculacao }: Props) {
  return (
    <BoxCaracteristica titulo="CFOP">
      <Flex flexDir="column" align="flex-start" w="152px">
        <Text pt="36px" fontWeight="semibold" fontSize="14px" color="gray.700">
          Nota Fiscal
        </Text>
        <Flex
          height="32px"
          border="1px solid #CCCCCC"
          borderRadius="4px"
          minW="152px"
          bg="gray.50"
          align="center"
          pl="12px"
          userSelect="none"
        >
          <Text fontSize="14px">
            {dadosVinculacao?.cfopNota || 'Não informado'}
          </Text>
        </Flex>
      </Flex>
      <Input
        id="cfop"
        name="cfop"
        label="CFOP de Entrada"
        fontSizeLabel="14px"
        fontWeightLabel="semibold"
        fontSize="14px"
        height="32px"
        pl="12px"
        placeholder="Não informado"
        maxLength={4}
      />
    </BoxCaracteristica>
  );
}
