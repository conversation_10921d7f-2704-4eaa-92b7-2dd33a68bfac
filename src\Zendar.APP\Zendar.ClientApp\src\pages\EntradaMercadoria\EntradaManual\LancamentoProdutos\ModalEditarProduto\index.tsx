import {
  ModalProps,
  ModalContent,
  ModalBody,
  useDisclosure,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  Button,
  GridItem,
  ModalCloseButton,
  FormLabel,
  Flex,
  HStack,
  Text,
} from '@chakra-ui/react';
import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import { CorOption, TamanhoOption } from 'helpers/api/Produto';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { SimpleCard } from 'components/update/Form/SimpleCard';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';

import { FormularioValoresFiscais } from '../ModalAdicionarProduto/components';

import { ListagemVariacoesAdicionadas } from './components/ListagemVariacoesAdicionadas';
import { yupResolver, FormData } from './validationForm';

type ProdutoResponse = {
  valorUnitarioEntrada: number;
  valorIpi: number;
  valorIcmsSt: number;
  valorFcpSt: number;
  custoAdicional: number;
  variacoes: {
    corId: string | null;
    tamanhoId: string | null;
    quantidade: number;
  }[];
  produtoId: string;
};

type ModalEditarProdutoResponse = {
  produtoEditado: ProdutoResponse;
};

type ModalEditarProdutoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<ModalEditarProdutoResponse> & {
    nomeProduto: string;
    produtoId: string;
    produtoTipoVariacao: boolean;
    variacoes: {
      cor: string | null;
      corId: string | null;
      nome: string;
      produtoCorTamanhoId: string;
      produtoId: string;
      quantidade: number;
      tamanho: string | null;
      tamanhoId: string | null;
    }[];
    cores: CorOption[];
    tamanhos: TamanhoOption[];
    dadosEntrada: {
      quantidade: number;
      valorUnitario: number;
      ipi: number;
      icmsSt: number;
      fcpSt: number;
      custoAdicional: number;
    };
    volumeUnitario?: boolean;
    entradaRateiaIcmsSt: boolean;
    casasDecimaisQuantidade: number;
    casasDecimaisValor: number;
  };

export const ModalEditarProduto = create<
  ModalEditarProdutoProps,
  ModalEditarProdutoResponse
>(
  ({
    onResolve,
    onReject,
    dadosEntrada,
    casasDecimaisQuantidade,
    casasDecimaisValor,
    volumeUnitario = true,
    nomeProduto,
    entradaRateiaIcmsSt,
    produtoTipoVariacao,
    variacoes,
    cores,
    tamanhos,
    produtoId,

    ...rest
  }) => {
    const formMethods = useForm<FormData>({
      resolver: yupResolver,
      defaultValues: dadosEntrada,
    });
    const { setValue, watch } = formMethods;
    const { cor: corEscolhida, tamanho: tamanhoEscolhido } = watch();

    const [listaVariacoes, setListaVariacoes] = useState(variacoes);
    const produtoTemCores = cores.length > 0;
    const produtoTemTamanhos = tamanhos.length > 0;

    const podeConfirmar = useCallback(() => {
      if (!produtoTipoVariacao) {
        return true;
      }

      if (produtoTemCores && produtoTemTamanhos) {
        return !!corEscolhida && !!tamanhoEscolhido;
      }

      if (produtoTemCores) {
        return !!corEscolhida;
      }

      if (produtoTemTamanhos) {
        return !!tamanhoEscolhido;
      }

      return false;
    }, [
      corEscolhida,
      produtoTemCores,
      produtoTemTamanhos,
      produtoTipoVariacao,
      tamanhoEscolhido,
    ])();

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const [isLoading, setIsLoading] = useState(false);

    const adicionarVariacaoNaLista = useCallback(() => {
      const data: FormData = watch();
      const novaVariacao = {
        cor: data.cor?.label || null,
        corId: data.cor?.value || null,
        nome: nomeProduto,
        produtoCorTamanhoId: '',
        produtoId,
        quantidade: data.quantidade,
        tamanho: data.tamanho?.label || null,
        tamanhoId: data.tamanho?.value || null,
      };
      setListaVariacoes((prev) => {
        const variacaoJaAdicionada = prev.find(
          (item) =>
            item.corId === novaVariacao.corId &&
            item.tamanhoId === novaVariacao.tamanhoId
        );
        if (variacaoJaAdicionada) {
          return prev.map((item) => {
            if (
              item.corId === novaVariacao.corId &&
              item.tamanhoId === novaVariacao.tamanhoId
            ) {
              return {
                ...item,
                quantidade: item.quantidade + novaVariacao.quantidade,
              };
            }
            return item;
          });
        }

        return [...prev, novaVariacao];
      });
      setValue('quantidade', 1);
      setValue('cor', null);
      setValue('tamanho', null);
    }, [nomeProduto, produtoId, watch, setValue]);

    const limparValoresFiscaisVoltarValorPrecoCompra = useCallback(() => {
      setValue('ipi', 0);
      setValue('icmsSt', 0);
      setValue('fcpSt', 0);
      setValue('custoAdicional', 0);
      setValue('valorUnitario', dadosEntrada?.valorUnitario || 0);
    }, [dadosEntrada?.valorUnitario, setValue]);

    const handleSubmit = formMethods.handleSubmit(async (data) => {
      setIsLoading(true);

      const produtoResponse: ProdutoResponse = {
        valorUnitarioEntrada: data.valorUnitario || 0,
        valorIpi: data.ipi || 0,
        valorIcmsSt: entradaRateiaIcmsSt ? 0 : data.icmsSt || 0,
        valorFcpSt: data.fcpSt || 0,
        custoAdicional: data.custoAdicional || 0,
        variacoes: listaVariacoes.map((item) => ({
          corId: item.corId,
          tamanhoId: item.tamanhoId,
          quantidade: produtoTipoVariacao ? item.quantidade : data.quantidade,
        })),
        produtoId,
      };

      onResolve({ produtoEditado: produtoResponse });
    });

    function handleCancelar() {
      onReject();
    }

    return (
      <ModalPadraoChakra
        isCentered
        size="full"
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent h="unset" bg="gray.100" borderRadius="0px">
          {isLoading && <LoadingPadrao />}
          <ModalHeader
            px="40px"
            py="20px"
            color="violet.500"
            fontWeight="normal"
            fontSize="18px"
          >
            Editar produto
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody px="40px" pt="0px">
            <FormProvider {...formMethods}>
              <SimpleCard
                boxShadow="none"
                bg="violet.500"
                p="16px"
                as={HStack}
                mb="24px"
              >
                <Flex flexDir="column" w="50%">
                  <Text color="white" fontSize="12px">
                    Produto:
                  </Text>
                  <Text
                    color="secondary.300"
                    fontSize="sm"
                    fontWeight="semibold"
                  >
                    {nomeProduto}
                  </Text>
                </Flex>
              </SimpleCard>

              {produtoTipoVariacao && (
                <FormLabel
                  fontSize="14px"
                  fontWeight="600"
                  color="black"
                  mt="19px"
                  mb="0px"
                >
                  Informe uma variação
                </FormLabel>
              )}
              <SimpleCard
                background={produtoTipoVariacao ? 'gray.200' : 'gray.100'}
                boxShadow="none"
                p="28px 16px"
                px={produtoTipoVariacao ? '40px' : '12px'}
                pb={produtoTipoVariacao ? '48px' : '28px'}
              >
                <SimpleGridForm>
                  {produtoTemCores && (
                    <SelectPadrao
                      id="cor"
                      name="cor"
                      label="Cor"
                      placeholder="Selecione uma cor cadastrada no produto"
                      colSpan={[12, 12, 4, 4]}
                      options={cores}
                      asControlledByObject
                      required
                      isDisabled={isLoading}
                      menuPortalTarget={document?.body}
                    />
                  )}
                  {produtoTemTamanhos && (
                    <SelectPadrao
                      id="tamanho"
                      name="tamanho"
                      label="Tamanho"
                      placeholder="Selecione um tamanho cadastrado no produto"
                      colSpan={[12, 12, 4, 4]}
                      options={tamanhos}
                      asControlledByObject
                      required
                      isDisabled={isLoading}
                      menuPortalTarget={document?.body}
                    />
                  )}

                  <NumberInput
                    id="quantidade"
                    name="quantidade"
                    label="Quantidade"
                    pl="12px"
                    textAlign="left"
                    placeholder={
                      volumeUnitario
                        ? '0'
                        : `0,${'0'.repeat(casasDecimaisQuantidade)}`
                    }
                    scale={volumeUnitario ? 0 : casasDecimaisQuantidade}
                    colSpan={[
                      12,
                      12,
                      produtoTemCores && produtoTemTamanhos ? 3 : 4,
                      produtoTemCores && produtoTemTamanhos ? 3 : 4,
                    ]}
                  />
                </SimpleGridForm>
                <SimpleGridForm mt="20px" columns={[12, 12, 12, 12]}>
                  <FormularioValoresFiscais
                    entradaRateiaIcmsSt={entradaRateiaIcmsSt}
                    casasDecimaisValor={casasDecimaisValor}
                    listaJaPossuiProdutoAdicionado={
                      produtoTipoVariacao && !!listaVariacoes?.length
                    }
                  />
                  {produtoTipoVariacao && (
                    <>
                      <GridItem colSpan={[12, 12, 2]} mt="18px" ml="0px">
                        <Button
                          colorScheme="teal"
                          borderRadius="full"
                          w="full"
                          height="36px"
                          fontSize="14px"
                          minW="176px"
                          onClick={adicionarVariacaoNaLista}
                          isDisabled={!podeConfirmar || !watch('quantidade')}
                          fontWeight="600"
                        >
                          Adicionar variação
                        </Button>
                      </GridItem>
                      <ListagemVariacoesAdicionadas
                        listaVariacoes={listaVariacoes}
                        setListaVariacoes={setListaVariacoes}
                        casasDecimaisQuantidade={casasDecimaisQuantidade}
                        limparValoresFiscaisVoltarValorPrecoCompra={
                          limparValoresFiscaisVoltarValorPrecoCompra
                        }
                      />
                    </>
                  )}
                </SimpleGridForm>
              </SimpleCard>
            </FormProvider>
          </ModalBody>
          <ModalFooter
            p="16px 32px"
            borderTop="1px"
            gap="24px"
            position="sticky"
            borderColor="purple.500"
            mx={{ base: 0, md: 8 }}
          >
            <Button
              colorScheme="gray"
              variant="outlineDefault"
              onClick={handleCancelar}
              borderRadius="full"
              minW="160px"
              height="32px"
              fontSize="14px"
              fontWeight="600"
              w={['full', 'full', '160px']}
            >
              Cancelar
            </Button>
            <Button
              colorScheme="secondary"
              borderRadius="full"
              w={['full', 'full', '160px']}
              height="32px"
              fontSize="14px"
              fontWeight="600"
              isDisabled={listaVariacoes?.length === 0}
              onClick={handleSubmit}
            >
              Confirmar
            </Button>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
