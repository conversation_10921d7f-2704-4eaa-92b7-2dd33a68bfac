import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { Cor, <PERSON><PERSON><PERSON> } from '../ModalAdicionarProduto/validationForm';

export type FormData = {
  quantidade: number;
  valorUnitario: number;
  ipi: number;
  icmsSt: number;
  fcpSt: number;
  custoAdicional: number;
  cor: Cor | null;
  tamanho: Tam<PERSON><PERSON> | null;
};

const schema = yup.object().shape({
  valorUnitario: yup.number().nullable(),
  ipi: yup.number().nullable(),
  icmsSt: yup.number().nullable(),
  fcpSt: yup.number().nullable(),
  custoAdicional: yup.number().nullable(),
});

export const yupResolver = yupResolverInstance(schema);
