import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';

export type ProdutoTamanhoProps = {
  id: string;
  descricao: string;
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
  padraoSistema: boolean;
  sequenciaOrdenacao: number;
};

export type TamanhoOption = {
  label: string;
  value: string;
  produtoCorTamanhoId: string;
};

/**
 * Busca os tamanhos de um produto específico
 * @param produtoId - ID do produto
 * @returns Array de opções de tamanho formatadas para uso em selects
 */
export const buscarTamanhosProduto = async (
  produtoId: string
): Promise<TamanhoOption[]> => {
  try {
    const response = await api.get<void, ResponseApi<ProdutoTamanhoProps[]>>(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/tamanhos`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response?.avisos) {
      response.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (response?.sucesso && response?.dados) {
      return response.dados
        .filter(({ padraoSistema }) => !padraoSistema)
        .map((tamanho, index) => ({
          label: tamanho.descricao,
          value: tamanho.id,
          produtoCorTamanhoId: response.dados[index].id,
        }));
    }

    return [];
  } catch (error) {
    toast.error('Ocorreu um erro ao buscar tamanhos do produto');
    return [];
  }
};
