import { Flex, Text } from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { NumberInput } from 'components/update/Input/NumberInput';

import { FormData, VinculacaoProps } from '../types';

import { BoxCaracteristica } from './BoxCaracteristica';

type Props = {
  dadosVinculacao: VinculacaoProps;
  estaEditando: boolean;
  formMethods: UseFormReturn<FormData, object>;
  casasDecimaisQuantidade: number;
};

export const BoxQuantidade = ({
  dadosVinculacao,
  formMethods,
  estaEditando,
  casasDecimaisQuantidade,
}: Props) => {
  return (
    <BoxCaracteristica titulo="QUANTIDADE">
      <Flex flexDir="column" align="flex-start" w="152px">
        <Text pt="36px" fontWeight="semibold" fontSize="14px" color="gray.700">
          Nota Fiscal
        </Text>
        <Flex
          height="32px"
          border="1px solid #CCCCCC"
          borderRadius="4px"
          minW="152px"
          bg="gray.50"
          align="center"
          pl="12px"
          userSelect="none"
        >
          <Text fontSize="14px">
            {DecimalMask(dadosVinculacao?.quantidadeNota, 4, 4)}
          </Text>
        </Flex>
      </Flex>
      <NumberInput
        id="quantidade"
        name="quantidade"
        label="Nova quantidade"
        fontSizeLabel="14px"
        placeholder={`0,${'0'.repeat(casasDecimaisQuantidade)}`}
        scale={casasDecimaisQuantidade}
        height="32px"
        bg="white"
        variant="grade"
        fontSize="14px"
        pl="12px"
        onValueChange={(value) => {
          const valorUnitario = (
            dadosVinculacao?.valorTotal / (Number(value) || 1)
          ).toFixed(casasDecimaisQuantidade);
          formMethods.setValue('valorUnitario', valorUnitario);
        }}
        isDisabled={estaEditando}
      />
    </BoxCaracteristica>
  );
};
