import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum, {
  StatusConsultaEnum as StatusConsultaEnumType,
} from 'constants/enum/tipoAcaoEstoque';

type CorProps = {
  id: string;
  descricao: string;
  descricaoEcommerce: string | null;
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
  padraoSistema: boolean;
  hexadecimal: string | null;
  imagem: string | null;
};

type TamanhoProps = {
  id: string;
  codigoGTINEAN: string | null;
  descricao: string;
  descricaoEcommerce: string | null;
  sequenciaOrdenacao: number | null;
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
  padraoSistema: boolean;
};

export type VariacaoProduto = {
  cor: CorProps;
  tamanhos?: TamanhoProps[];
};

export type ParametrosObterVariacoes = {
  produtoId: string;
  status?: StatusConsultaEnumType;
};

/**
 * Obtém as variações (cores e tamanhos) de um produto
 * @param parametros - Parâmetros da consulta
 * @returns Array de variações do produto com cores e tamanhos
 */
export const buscarVariacoesProduto = async (
  parametros: ParametrosObterVariacoes
): Promise<VariacaoProduto[]> => {
  const { produtoId, status = StatusConsultaEnum.ATIVOS } = parametros;

  try {
    const query = new URLSearchParams({
      status: status.toString(),
    });

    const response = await api.get<void, ResponseApi<VariacaoProduto[]>>(
      `${ConstanteEnderecoWebservice.OBTER_PRODUTO_COR_TAMANHO_GTIN_EAN.replace(
        '{id}',
        produtoId
      )}?${query.toString()}`
    );

    if (response?.avisos) {
      response.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (response?.sucesso && response?.dados) {
      return response.dados;
    }

    return [];
  } catch (error) {
    toast.error('Ocorreu um erro ao obter variações do produto');
    return [];
  }
};
