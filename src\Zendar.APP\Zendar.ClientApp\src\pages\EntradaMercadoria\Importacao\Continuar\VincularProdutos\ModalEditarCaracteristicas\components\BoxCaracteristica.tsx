import { Box, VStack } from '@chakra-ui/react';
import { ReactNode } from 'react';

interface Props {
  titulo: string;
  children: ReactNode;
}

export function BoxCaracteristica({ titulo, children }: Props) {
  return (
    <VStack
      width="200px"
      height="200px"
      bg="gray.50"
      px="24px"
      py={0}
      gap="20px"
      position="relative"
      borderWidth="1px"
      borderColor="gray.200"
      borderRadius="5px"
    >
      <Box
        width="160px"
        height="24px"
        bg="violet.500"
        lineHeight="24px"
        textColor="white"
        fontSize="14px"
        borderRadius="full"
        textAlign="center"
        fontWeight="semibold"
        position="absolute"
        marginTop="-12px"
      >
        {titulo}
      </Box>
      {children}
    </VStack>
  );
}
