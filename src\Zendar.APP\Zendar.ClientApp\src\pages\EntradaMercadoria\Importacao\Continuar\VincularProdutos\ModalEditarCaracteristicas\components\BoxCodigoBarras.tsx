import { Flex, Text } from '@chakra-ui/react';

import { NumberInput } from 'components/update/Input/NumberInput';

import { VinculacaoProps } from '../types';

import { BoxCaracteristica } from './BoxCaracteristica';

interface Props {
  dadosVinculacao: VinculacaoProps;
}

export function BoxCodigoBarras({ dadosVinculacao }: Props) {
  return (
    <BoxCaracteristica titulo="CÓD. DE BARRAS">
      <Flex flexDir="column" align="flex-start" w="152px">
        <Text pt="36px" fontWeight="semibold" fontSize="14px" color="gray.700">
          Nota Fiscal
        </Text>
        <Flex
          height="32px"
          border="1px solid #CCCCCC"
          borderRadius="4px"
          minW="152px"
          bg="gray.50"
          align="center"
          pl="12px"
          userSelect="none"
        >
          <Text fontSize="14px">
            {dadosVinculacao?.codigoBarrasNota || 'Não informado'}
          </Text>
        </Flex>
      </Flex>
      <NumberInput
        label="Novo Código de Barras"
        id="codigoBarras"
        name="codigoBarras"
        fontSizeLabel="14px"
        placeholder="Não informado"
        scale={0}
        height="32px"
        bg="white"
        variant="grade"
        fontSize="14px"
        textAlign="left"
        pl="12px"
        precision={14}
        canBeUndefined
      />
    </BoxCaracteristica>
  );
}
