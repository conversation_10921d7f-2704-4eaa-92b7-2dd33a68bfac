import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';

export type CorDetalhes = {
  id: string;
  descricao: string;
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
  padraoSistema: boolean;
  hexadecimal?: string | null;
  imagem?: string | null;
};

export type ProdutoCoresProps = {
  id: string;
  produtoId: string;
  ativo: boolean;
  corId: string;
  cor: CorDetalhes;
  produtoCorImagens?: string | null;
  produtoCorTamanhos?: string | null;
};

export type CorOption = {
  label: string;
  value: string;
  produtoCorId: string;
};

/**
 * Busca as cores de um produto específico
 * @param produtoId - ID do produto
 * @returns Array de opções de cor formatadas para uso em selects
 */
export const buscarCoresProduto = async (
  produtoId: string
): Promise<CorOption[]> => {
  try {
    const response = await api.get<void, ResponseApi<ProdutoCoresProps[]>>(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/cores`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response?.avisos) {
      response.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (response?.sucesso && response?.dados) {
      return response.dados
        .filter(({ cor }) => !cor.padraoSistema)
        .map(({ cor }, index) => ({
          label: cor.descricao,
          value: cor.id,
          produtoCorId: response.dados[index].id,
        }));
    }

    return [];
  } catch (error) {
    toast.error('Ocorreu um erro ao buscar cores do produto');
    return [];
  }
};
