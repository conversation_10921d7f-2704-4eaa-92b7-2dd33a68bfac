import { toast } from 'react-toastify';

import { buscarCoresProduto, CorOption } from './buscarCoresProduto';
import { buscarTamanhosProduto, TamanhoOption } from './buscarTamanhosProduto';

export type VariacoesProdutoResult = {
  tamanhos: TamanhoOption[];
  cores: CorOption[];
};

/**
 * <PERSON><PERSON> tanto as cores quanto os tamanhos de um produto em paralelo
 * @param produtoId - ID do produto
 * @returns Objeto contendo arrays de cores e tamanhos
 */
export const buscarCoresTamanhosProduto = async (
  produtoId: string
): Promise<VariacoesProdutoResult> => {
  try {
    const [tamanhos, cores] = await Promise.all([
      buscarTamanhosProduto(produtoId),
      buscarCoresProduto(produtoId),
    ]);

    return {
      tamanhos,
      cores,
    };
  } catch (error) {
    toast.error('Ocorreu um erro ao buscar variações do produto');
    return {
      tamanhos: [],
      cores: [],
    };
  }
};
