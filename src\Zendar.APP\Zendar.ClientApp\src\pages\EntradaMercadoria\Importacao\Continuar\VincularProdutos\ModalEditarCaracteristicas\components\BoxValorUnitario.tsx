import { Flex, Text } from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';

import { NumberInput } from 'components/update/Input/NumberInput';

import { VinculacaoProps, FormData } from '../types';

import { BoxCaracteristica } from './BoxCaracteristica';

type Props = {
  dadosVinculacao: VinculacaoProps;
  estaEditando: boolean;
  formMethods: UseFormReturn<FormData, object>;
  casasDecimaisValor: number;
};

export function BoxValorUnitario({
  dadosVinculacao,
  formMethods,
  estaEditando,
  casasDecimaisValor,
}: Props) {
  return (
    <BoxCaracteristica titulo="VALOR UNITÁRIO">
      <Flex flexDir="column" align="flex-start">
        <Text pt="36px" fontWeight="semibold" fontSize="14px" color="gray.700">
          Nota Fiscal
        </Text>
        <Flex
          height="32px"
          border="1px solid #CCCCCC"
          borderRadius="4px"
          color="gray.300"
          minW="152px"
          bg="gray.50"
          align="center"
          userSelect="none"
        >
          <Flex
            fontSize="12px"
            borderRight="1px solid #CCCCCC"
            px="5px"
            minW="34px"
            justifyContent="center"
            alignItems="center"
            height="full"
          >
            R$
          </Flex>
          <Text fontSize="14px" textAlign="right" w="full" pr="12px">
            {Number(dadosVinculacao?.valorUnitarioNota).toLocaleString(
              'pt-BR',
              {
                minimumIntegerDigits: 1,
                maximumFractionDigits: 2,
                minimumFractionDigits: 2,
              }
            )}
          </Text>
        </Flex>
      </Flex>
      <NumberInput
        id="valorUnitario"
        name="valorUnitario"
        leftElement="R$"
        placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
        scale={casasDecimaisValor}
        fontSizeLabel="14px"
        labelColor="black"
        height="32px"
        leftElementColor="gray.300"
        leftElementFontSize="12px"
        borderRightForLeftElement="gray.200"
        leftElementHeight="32px"
        exibirBordaRightForLeftElement
        label="Novo valor unitário"
        width="152px"
        fontSize="14px"
        isDisabled={estaEditando}
        variant="grade"
        bg="white"
        onValueChange={(value) => {
          const quantidade = Number(
            (dadosVinculacao?.valorTotal / (Number(value) || 1)).toFixed(
              casasDecimaisValor
            )
          );
          formMethods.setValue('quantidade', quantidade);
        }}
      />
    </BoxCaracteristica>
  );
}
