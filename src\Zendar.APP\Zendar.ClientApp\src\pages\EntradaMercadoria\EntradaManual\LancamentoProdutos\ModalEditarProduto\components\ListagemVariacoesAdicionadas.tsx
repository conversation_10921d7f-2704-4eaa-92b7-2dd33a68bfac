import { Flex, Text, Box, GridItem, Tag } from '@chakra-ui/react';
import React from 'react';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import ShadowScrollbar from 'components/PDV/Geral/ShadowScrollbar';

import { LixeiraIcon } from 'icons';

export const ListagemVariacoesAdicionadas = ({
  listaVariacoes,
  setListaVariacoes,
  casasDecimaisQuantidade,
  limparValoresFiscaisVoltarValorPrecoCompra,
}: {
  listaVariacoes: {
    cor: string | null;
    corId: string | null;
    nome: string;
    produtoCorTamanhoId: string;
    produtoId: string;
    quantidade: number;
    tamanho: string | null;
    tamanhoId: string | null;
  }[];
  setListaVariacoes: React.Dispatch<
    React.SetStateAction<
      {
        cor: string | null;
        corId: string | null;
        nome: string;
        produtoCorTamanhoId: string;
        produtoId: string;
        quantidade: number;
        tamanho: string | null;
        tamanhoId: string | null;
      }[]
    >
  >;
  casasDecimaisQuantidade: number;
  limparValoresFiscaisVoltarValorPrecoCompra: () => void;
}) => {
  return (
    <GridItem colSpan={12} maxW="full">
      {listaVariacoes.length > 0 && (
        <Box>
          <Flex flexDirection="column" bg="white" borderRadius="5px">
            <Flex
              gap="10px"
              px="24px"
              mx="24px"
              height="34px"
              w="calc(100% - 48px)"
              pt="16px"
            >
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW={['30%', '54%']}
              >
                Variação
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                minW={['30%', '45%']}
                fontWeight="500"
              >
                Quantidade
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW="16px"
                aria-label="Ações"
              />
            </Flex>
            <ShadowScrollbar
              maxHeight={350}
              paddingTop="0"
              shadowTopStyle={{
                background:
                  'linear-gradient(rgba(255, 255, 255, 0.74) 0%, rgb(255, 255, 255) 100%)',
                height: 40,
              }}
              shadowBottomStyle={{
                background:
                  'linear-gradient(to top, rgb(255 255 255 / 78%) 0%, rgb(255 255 255) 100%)',
                height: 40,
              }}
            >
              <Box>
                {listaVariacoes.map((variacao) => {
                  const excluirVariacao = (produtoCorTamanhoId: string) => {
                    setListaVariacoes((prev) => {
                      const novaLista = prev.filter(
                        (item) =>
                          item.produtoCorTamanhoId !== produtoCorTamanhoId
                      );

                      if (novaLista.length === 0) {
                        limparValoresFiscaisVoltarValorPrecoCompra();
                      }

                      return novaLista;
                    });
                  };

                  return (
                    <Flex
                      key={variacao.produtoCorTamanhoId}
                      gap="10px"
                      px="24px"
                      w="calc(100% - 48px)"
                      mx="24px"
                      borderTop="1px"
                      borderColor="gray.100"
                      height="54px"
                      align="center"
                    >
                      <Flex
                        fontSize="14px"
                        gap="8px"
                        color="gray.700"
                        minW={['30%', '54%']}
                      >
                        {variacao.cor && (
                          <Tag
                            colorScheme="teal"
                            bg="teal.500"
                            color="white"
                            borderRadius="10px"
                            minW="40px"
                            padding="0px 8px"
                            justifyContent="center"
                            size="sm"
                          >
                            {variacao.cor}
                          </Tag>
                        )}
                        {variacao.tamanho && (
                          <Tag
                            colorScheme="pink"
                            bg="pink.500"
                            color="white"
                            borderRadius="10px"
                            minW="40px"
                            padding="0px 8px"
                            justifyContent="center"
                            size="sm"
                          >
                            {variacao.tamanho}
                          </Tag>
                        )}
                      </Flex>

                      <Text
                        fontSize="14px"
                        color="gray.700"
                        fontWeight="bold"
                        minW={['30%', '45%']}
                      >
                        {DecimalMask(
                          variacao.quantidade,
                          casasDecimaisQuantidade
                        )}
                      </Text>
                      <Box>
                        <Flex justify="flex-end">
                          <LixeiraIcon
                            fontSize="16px"
                            cursor="pointer"
                            color="gray.500"
                            style={{
                              transition: 'color 0.2s',
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = '#E53E3E';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = '#718096';
                            }}
                            onClick={() =>
                              excluirVariacao(variacao.produtoCorTamanhoId)
                            }
                          />
                        </Flex>
                      </Box>
                    </Flex>
                  );
                })}
              </Box>
            </ShadowScrollbar>

            <Flex
              gap="10px"
              px="24px"
              w="calc(100% - 48px)"
              mx="24px"
              borderTop="1px"
              borderColor="gray.100"
              height="54px"
              align="center"
            >
              <Text
                minW={['30%', '54%']}
                textAlign="end"
                fontSize="14px"
                color="gray.700"
              >
                Quantidade total:
              </Text>
              <Text
                minW={['30%', '45%']}
                fontSize="14px"
                fontWeight="bold"
                color="black"
              >
                {DecimalMask(
                  listaVariacoes.reduce(
                    (acc, curr) => acc + curr.quantidade,
                    0
                  ),
                  casasDecimaisQuantidade
                )}
              </Text>
              <Text minW="16px" />
            </Flex>
          </Flex>
        </Box>
      )}
    </GridItem>
  );
};
